package com.tinyzk.user.center.consumer;

import com.alibaba.fastjson2.JSON;
import com.tinyzk.user.center.dto.ResumeParseMessage;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.dto.UserDuplicationCheckDTO;
import com.tinyzk.user.center.entity.ResumeParseRecords;
import com.tinyzk.user.center.entity.UserAuth;
import com.tinyzk.user.center.entity.UserBase;
import com.tinyzk.user.center.entity.UserProfile;
import com.tinyzk.user.center.common.enums.IdentityType;
import com.tinyzk.user.center.common.enums.UserStatus;
import com.tinyzk.user.center.common.exception.ErrorCode;
import com.tinyzk.user.center.common.exception.ResumeParseException;
import com.tinyzk.user.center.mapper.ResumeParseRecordsMapper;
import com.tinyzk.user.center.mapper.UserAuthMapper;
import com.tinyzk.user.center.mapper.UserBaseMapper;
import com.tinyzk.user.center.mapper.UserProfileMapper;
import com.tinyzk.user.center.service.BatchResumeParseService;
import com.tinyzk.user.center.service.OSSFileStorageService;
import com.tinyzk.user.center.service.ResumePersistenceService;
import com.tinyzk.user.center.service.ThirdPartyResumeParseService;
import com.tinyzk.user.center.common.constants.CommonConstants;
import com.tinyzk.user.center.vo.BatchResumeUploadResultVO;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.time.LocalDateTime;

/**
 * 简历解析消息消费者
 * 注意：已迁移到阿里云官方客户端，由AliyunConsumerService统一管理
 * 此类保留业务逻辑，但不再直接作为消费者
 */
@Component
@Slf4j
// @RequiredArgsConstructor
public class ResumeParseMessageConsumer {

    private final ThirdPartyResumeParseService parseService;
    private final OSSFileStorageService ossService;
    private final ResumePersistenceService persistenceService;
    private final ResumeParseRecordsMapper parseRecordsMapper;
    private final BatchResumeParseService batchResumeParseService;
    private final UserBaseMapper userBaseMapper;
    private final UserAuthMapper userAuthMapper;
    private final RateLimiter consumerLimiter;
    private final CircuitBreaker apiCircuitBreaker;
    private final MeterRegistry meterRegistry;
    public ResumeParseMessageConsumer(ThirdPartyResumeParseService parseService,
                                    OSSFileStorageService ossService,
                                    ResumePersistenceService persistenceService,
                                    ResumeParseRecordsMapper parseRecordsMapper,
                                    BatchResumeParseService batchResumeParseService,
                                    UserBaseMapper userBaseMapper,
                                    UserAuthMapper userAuthMapper,
                                    UserProfileMapper userProfileMapper,
                                    MeterRegistry meterRegistry) {
        log.info("初始化简历解析消息消费者...");
        this.parseService = parseService;
        this.ossService = ossService;
        this.persistenceService = persistenceService;
        this.parseRecordsMapper = parseRecordsMapper;
        this.batchResumeParseService = batchResumeParseService;
        this.userBaseMapper = userBaseMapper;
        this.userAuthMapper = userAuthMapper;
        this.meterRegistry = meterRegistry;
        // 使用Resilience4j的RateLimiter
        this.consumerLimiter = RateLimiter.of("message-consumer",
            RateLimiterConfig.custom()
                .limitForPeriod(30)
                .limitRefreshPeriod(java.time.Duration.ofSeconds(1))
                .timeoutDuration(java.time.Duration.ofMillis(100))
                .build());

        // 使用Resilience4j的CircuitBreaker
        this.apiCircuitBreaker = CircuitBreaker.of("third-party-api",
            CircuitBreakerConfig.custom()
                .failureRateThreshold(60)
                .waitDurationInOpenState(java.time.Duration.ofSeconds(60))
                .slidingWindowSize(10)
                .minimumNumberOfCalls(5)
                .build());

        log.info("简历解析消息消费者初始化完成，监听主题: RESUME_PARSE_TOPIC, 消费者组: resume-parse-consumer-group");
    }

    // 移除@Override注解，改为普通的业务处理方法
    public void processMessage(ResumeParseMessage message) {
        Timer.Sample sample = Timer.start(meterRegistry);
        String messageId = message.getMessageId();

        try {
            log.info("开始处理简历解析消息: messageId={}, batchId={}, fileName={}",
                    messageId, message.getBatchId(), message.getFileName());

            // 使用Resilience4j限流控制
            consumerLimiter.executeRunnable(() -> {
                processResumeParseMessage(message);
            });

            meterRegistry.counter("mq.message.consume.success",
                "topic", "RESUME_PARSE_TOPIC").increment();

            log.info("简历解析消息处理完成: messageId={}", messageId);

        } catch (CallNotPermittedException e) {
            log.warn("第三方API熔断，消息将重试: messageId={}", messageId);
            meterRegistry.counter("mq.message.consume.circuit_breaker",
                "topic", "RESUME_PARSE_TOPIC").increment();
            throw new RuntimeException("API熔断，触发重试", e);

        } catch (Exception e) {
            log.error("处理简历解析消息失败: messageId={}", messageId, e);
            meterRegistry.counter("mq.message.consume.failure",
                "topic", "RESUME_PARSE_TOPIC").increment();

            // 根据错误类型决定是否重试
            if (isRetryableError(e)) {
                throw e; // 触发重试
            } else {
                // 不可重试错误，记录到死信队列
                handleNonRetryableError(message, e);
            }
        } finally {
            sample.stop(Timer.builder("mq.message.consume.duration")
                .tag("topic", "RESUME_PARSE_TOPIC")
                .register(meterRegistry));
        }
    }

    /**
     * 处理简历解析消息的核心逻辑
     */
    private void processResumeParseMessage(ResumeParseMessage message) {
        Long parseRecordId = null;

        try {
            // 1. 创建解析记录
            parseRecordId = createParseRecord(message);

            // 2. 从OSS下载文件
            byte[] fileContent = downloadFileFromOSS(message);

            // 3. 创建MultipartFile对象
            MultipartFile multipartFile = createMultipartFile(message, fileContent);

            // 4. 调用第三方API解析简历
            ThirdPartyParseResultDTO parseResult = callThirdPartyApiWithCircuitBreaker(multipartFile);

            // 5. 更新解析记录状态
            updateParseRecordWithResult(parseRecordId, parseResult);

            // 6. 处理用户数据和保存解析结果
            Long finalUserId = handleUserDataAndSaveResults(message, parseResult, parseRecordId);

            log.info("简历解析处理成功: messageId={}, parseRecordId={}, userId={}",
                    message.getMessageId(), parseRecordId, finalUserId);

        } catch (Exception e) {
            // 更新解析记录为失败状态
            if (parseRecordId != null) {
                updateParseRecordWithError(parseRecordId, e);
            }
            throw e;
        }
    }

    /**
     * 处理用户数据和保存解析结果
     * 参考 BatchResumeParseServiceImpl.processFile 的逻辑
     */
    private Long handleUserDataAndSaveResults(ResumeParseMessage message, ThirdPartyParseResultDTO parseResult, Long parseRecordId) {
        try {
            Long finalUserId = message.getUserId();

            // 如果消息中没有用户ID（批量上传场景），需要创建或查找用户
            if (finalUserId == null) {
                finalUserId = handleBatchUploadUser(parseResult, parseRecordId);
            }

            // 保存转换后的解析数据
            if (finalUserId != null) {
                persistenceService.saveConvertedData(finalUserId, parseResult, message);

                // 更新解析记录的用户ID
                updateParseRecordUserId(parseRecordId, finalUserId);
            }

            return finalUserId;

        } catch (Exception e) {
            log.error("处理用户数据和保存解析结果失败: messageId={}, parseRecordId={}",
                    message.getMessageId(), parseRecordId, e);
            throw e;
        }
    }

    /**
     * 处理批量上传场景下的用户创建或查找
     */
    private Long handleBatchUploadUser(ThirdPartyParseResultDTO parseResult, Long parseRecordId) {
        try {
            // 从解析结果中提取用户信息
            BatchResumeUploadResultVO.UserInfoSummary userInfo = extractUserInfo(parseResult);

            // 检查用户是否重复
            UserDuplicationCheckDTO.CheckResult duplicationResult = checkUserDuplication(userInfo);

            if (duplicationResult.getIsDuplicate()) {
                // 用户已存在，返回现有用户ID
                log.info("发现重复用户，使用现有用户: userId={}, matchType={}",
                        duplicationResult.getDuplicateUserId(), duplicationResult.getMatchType());
                return duplicationResult.getDuplicateUserId();
            } else {
                // 创建新用户
                return createNewUser(parseResult);
            }

        } catch (Exception e) {
            log.error("处理批量上传用户失败: parseRecordId={}", parseRecordId, e);
            throw new ResumeParseException(ErrorCode.BATCH_RESUME_USER_CREATE_FAILED);
        }
    }

    /**
     * 创建解析记录
     */
    private Long createParseRecord(ResumeParseMessage message) {
        ResumeParseRecords record = new ResumeParseRecords();
        record.setUserId(message.getUserId());
        record.setOriginalFilename(message.getFileName());
        record.setFileSize(message.getFileSize());
        record.setFileType(message.getFileType());
        record.setParseStatus(1); // 解析中
        record.setCreatedAt(LocalDateTime.now());

        parseRecordsMapper.insert(record);
        return record.getRecordId();
    }

    /**
     * 从OSS下载文件
     */
    private byte[] downloadFileFromOSS(ResumeParseMessage message) {
        try {
            return ossService.downloadFile(message.getOssKey());
        } catch (Exception e) {
            log.error("从OSS下载文件失败: ossKey={}", message.getOssKey(), e);
            throw new RuntimeException("文件下载失败", e);
        }
    }

    /**
     * 创建MultipartFile对象
     */
    private MultipartFile createMultipartFile(ResumeParseMessage message, byte[] content) {
        return new MultipartFile() {
            @Override
            public String getName() { return "file"; }

            @Override
            public String getOriginalFilename() { return message.getFileName(); }

            @Override
            public String getContentType() { return ResumeParseMessageConsumer.this.getContentType(message.getFileType()); }

            @Override
            public boolean isEmpty() { return content.length == 0; }

            @Override
            public long getSize() { return content.length; }

            @Override
            public byte[] getBytes() { return content; }

            @Override
            public java.io.InputStream getInputStream() {
                return new ByteArrayInputStream(content);
            }

            @Override
            public void transferTo(java.io.File dest) throws IOException {
                throw new UnsupportedOperationException("transferTo not supported");
            }
        };
    }

    /**
     * 使用熔断器保护的第三方API调用
     */
    private ThirdPartyParseResultDTO callThirdPartyApiWithCircuitBreaker(MultipartFile file) {
        return apiCircuitBreaker.executeSupplier(() -> {
            return parseService.parseResumeWithRetry(file);
        });
    }

    /**
     * 更新解析记录（成功）
     */
    private void updateParseRecordWithResult(Long recordId, ThirdPartyParseResultDTO result) {
        try {
            ResumeParseRecords record = new ResumeParseRecords();
            record.setRecordId(recordId);
            record.setParseStatus(result.getErrorCode() == 0 ? 2 : 3); // 2-成功, 3-失败
            record.setParseResult(JSON.toJSONString(result));
            record.setThirdPartyId(result.getCvId());
            record.setErrorMessage(result.getErrorMessage());
            record.setUpdatedAt(LocalDateTime.now());
            
            parseRecordsMapper.updateById(record);
        } catch (Exception e) {
            log.error("更新解析记录失败: recordId={}", recordId, e);
        }
    }

    /**
     * 更新解析记录（失败）
     */
    private void updateParseRecordWithError(Long recordId, Exception error) {
        try {
            ResumeParseRecords record = new ResumeParseRecords();
            record.setRecordId(recordId);
            record.setParseStatus(3); // 失败
            record.setErrorMessage(error.getMessage());
            record.setUpdatedAt(LocalDateTime.now());
            
            parseRecordsMapper.updateById(record);
        } catch (Exception e) {
            log.error("更新解析记录失败状态失败: recordId={}", recordId, e);
        }
    }

    

    /**
     * 判断是否为可重试错误
     */
    private boolean isRetryableError(Exception e) {
        return e instanceof SocketTimeoutException ||
               e instanceof ConnectException ||
               e instanceof org.springframework.web.client.HttpServerErrorException ||
               (e instanceof RuntimeException && e.getMessage().contains("API熔断"));
    }

    /**
     * 处理不可重试错误
     */
    private void handleNonRetryableError(ResumeParseMessage message, Exception e) {
        try {
            log.error("不可重试错误，记录到死信队列: messageId={}, error={}",
                    message.getMessageId(), e.getMessage());

            // 记录死信消息到数据库或其他存储
            // 这里可以扩展为发送到死信队列主题
            meterRegistry.counter("mq.message.dead_letter",
                "topic", "RESUME_PARSE_TOPIC").increment();

        } catch (Exception ex) {
            log.error("处理死信消息失败: messageId={}", message.getMessageId(), ex);
        }
    }

    /**
     * 根据文件类型获取Content-Type
     */
    private String getContentType(String fileType) {
        if (fileType == null) {
            return "application/octet-stream";
        }

        switch (fileType.toLowerCase()) {
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "txt":
                return "text/plain";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 更新解析记录的用户ID
     */
    private void updateParseRecordUserId(Long parseRecordId, Long userId) {
        ResumeParseRecords record = new ResumeParseRecords();
        record.setRecordId(parseRecordId);
        record.setUserId(userId);
        record.setUpdatedAt(LocalDateTime.now());

        parseRecordsMapper.updateById(record);
    }

    /**
     * 从解析结果中提取用户信息
     */
    private BatchResumeUploadResultVO.UserInfoSummary extractUserInfo(ThirdPartyParseResultDTO parseResult) {
        BatchResumeUploadResultVO.UserInfoSummary userInfo = new BatchResumeUploadResultVO.UserInfoSummary();

        if (parseResult.getParsingResult() != null) {
            ThirdPartyParseResultDTO.ParsingResult result = parseResult.getParsingResult();

            // 提取基本信息
            if (result.getBasicInfo() != null) {
                userInfo.setName(result.getBasicInfo().getName());
                userInfo.setGender(result.getBasicInfo().getGender());
                userInfo.setAge(result.getBasicInfo().getAge());
            }

            // 提取联系信息
            if (result.getContactInfo() != null) {
                userInfo.setPhone(result.getContactInfo().getPhoneNumber());
                userInfo.setEmail(result.getContactInfo().getEmail());
            }
        }

        return userInfo;
    }

    /**
     * 检查用户重复性
     */
    private UserDuplicationCheckDTO.CheckResult checkUserDuplication(BatchResumeUploadResultVO.UserInfoSummary userInfo) {
        UserDuplicationCheckDTO checkDTO = new UserDuplicationCheckDTO();
        checkDTO.setPhone(userInfo.getPhone());
        checkDTO.setEmail(userInfo.getEmail());
        checkDTO.setRealName(userInfo.getName());

        return batchResumeParseService.checkUserDuplication(checkDTO);
    }

    /**
     * 创建新用户
     */
    private Long createNewUser(ThirdPartyParseResultDTO parseResult) {
        try {
            // 创建用户基础信息
            UserBase userBase = new UserBase();
            userBase.setStatus(UserStatus.NORMAL.getCode());
            userBaseMapper.insert(userBase);
            Long userId = userBase.getUserId();

            log.info(CommonConstants.LogMessages.USER_CREATED, userId);

            // 创建用户认证信息（如果有邮箱或手机号）
            if (parseResult.getParsingResult() != null && parseResult.getParsingResult().getContactInfo() != null) {
                createUserAuth(userId, parseResult.getParsingResult().getContactInfo());
            }

            // 创建用户资料
            createUserProfile(userId, parseResult);

            return userId;

        } catch (Exception e) {
            log.error(CommonConstants.ErrorMessages.CREATE_USER_FAILED, e);
            throw new ResumeParseException(ErrorCode.BATCH_RESUME_USER_CREATE_FAILED);
        }
    }

    /**
     * 创建用户认证信息
     */
    private void createUserAuth(Long userId, ThirdPartyParseResultDTO.ContactInfo contactInfo) {
        try {
            // 如果有邮箱，创建邮箱认证信息
            if (contactInfo.getEmail() != null && !contactInfo.getEmail().trim().isEmpty()) {
                String email = contactInfo.getEmail().trim();

                // 检查邮箱是否已存在
                if (!isEmailExists(email)) {
                    UserAuth emailAuth = new UserAuth();
                    emailAuth.setUserId(userId);
                    emailAuth.setIdentityType(IdentityType.EMAIL.name());
                    emailAuth.setIdentifier(email);
                    emailAuth.setVerified(CommonConstants.VerificationStatus.UNVERIFIED);
                    emailAuth.setLastLoginAt(LocalDateTime.now());

                    userAuthMapper.insert(emailAuth);
                    log.info("创建邮箱认证信息成功: userId={}, email={}", userId, email);
                } else {
                    log.warn("邮箱已存在，跳过创建认证信息: email={}", email);
                }
            }

            // 如果有手机号，也创建手机号认证信息
            if (contactInfo.getPhoneNumber() != null && !contactInfo.getPhoneNumber().trim().isEmpty()) {
                String phone = contactInfo.getPhoneNumber().trim();

                // 检查手机号是否已存在
                if (!isPhoneExists(phone)) {
                    UserAuth phoneAuth = new UserAuth();
                    phoneAuth.setUserId(userId);
                    phoneAuth.setIdentityType(IdentityType.PHONE.name());
                    phoneAuth.setIdentifier(phone);
                    phoneAuth.setVerified(CommonConstants.VerificationStatus.UNVERIFIED);
                    phoneAuth.setLastLoginAt(LocalDateTime.now());

                    userAuthMapper.insert(phoneAuth);
                    log.info("创建手机号认证信息成功: userId={}, phone={}", userId, phone);
                } else {
                    log.warn("手机号已存在，跳过创建认证信息: phone={}", phone);
                }
            }

        } catch (Exception e) {
            log.error("创建用户认证信息失败: userId={}", userId, e);
            // 不抛出异常，避免影响整个用户创建流程
        }
    }
}
